import { motion } from 'framer-motion';
import { FaCalculator, FaCreativeCommonsZero, FaFileInvoiceDollar, FaRobot, FaTachometerAlt } from 'react-icons/fa';

const sidebarItems = [
  {
    id: 0,
    title: 'Dashboard',
    icon: <FaTachometerAlt size={20} />,
    route: '/dashboard'
  },
  {
    id: 1,
    title: 'Sales Invoice Generator',
    icon: <FaFileInvoiceDollar size={20} />,
    route: '/sales-invoice-generator'
  },
  {
    id: 2,
    title: 'Invoice Automation',
    icon: <FaRobot size={20} />,
    route: '/invoice-automation'
  },
  {
    id: 3,
    title: 'Accounting & Tax Assistant',
    icon: <FaCalculator size={20} />,
    route: '/accounting-tax-assistant'
  },
  {
    id: 4,
    title: 'Xero Automation',
    icon: <FaCreativeCommonsZero size={20} />,
    route: '/xero'
  }
];

export default function Sidebar({ activeItem, setActiveItem }) {
  return (
    <div className="bg-white rounded-xl shadow-lg p-4 h-full w-64">
      <div className="flex items-center justify-center mb-8 mt-4">
        <h1 
          className="text-3xl font-bold text-transparent bg-clip-text"
          style={{
            backgroundImage: 'linear-gradient(to right, #735D78, #588B8B)'
          }}
        >
          MizuFlow
        </h1>
      </div>
      
      <div className="space-y-2">
        {sidebarItems.map((item) => (
          <motion.div
            key={item.id}
            className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer ${
              activeItem === item.id 
                ? 'bg-gradient-to-r from-teal-50 to-cyan-50 text-teal-700' 
                : 'text-gray-600 hover:bg-gray-50'
            }`}
            onClick={() => setActiveItem(item.id)}
            whileHover={{ 
              x: 5,
              transition: { duration: 0.2 }
            }}
            whileTap={{ scale: 0.98 }}
          >
            <div className={`${
              activeItem === item.id 
                ? 'text-teal-700' 
                : 'text-gray-500'
            }`}>
              {item.icon}
            </div>
            <span className="font-medium">
              {item.title}
            </span>
          </motion.div>
        ))}
      </div>
    </div>
  );
} 