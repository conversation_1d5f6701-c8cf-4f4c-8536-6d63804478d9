import { motion } from "framer-motion";
import { useEffect, useState } from "react";

const EmailSending = ({ variants, invoiceData, emailTemplate, onComplete }) => {
  const [sending, setSending] = useState(false);
  const [sendingStatus, setSendingStatus] = useState([]);
  const [allSent, setAllSent] = useState(false);
  const [error, setError] = useState("");
  
  const { invoices } = invoiceData;
  
  // Initialize sending status
  useEffect(() => {
    if (invoices && invoices.length > 0 && sendingStatus.length === 0) {
      setSendingStatus(invoices.map(invoice => ({ 
        id: invoice.id,
        sent: false,
        sending: false,
        error: null
      })));
    }
  }, [invoices, sendingStatus]);

  // Dummy function to simulate sending an email
  const sendEmail = async (invoiceId, delay = 1000) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Random success (90% chance)
        if (Math.random() > 0.1) {
          resolve();
        } else {
          reject(new Error("Failed to send email"));
        }
      }, delay);
    });
  };

  // Send a single email
  const sendSingleEmail = async (index) => {
    // Mark as sending using functional update for latest state
    setSendingStatus(prevStatus => {
      const newStatus = [...prevStatus];
      newStatus[index] = { ...newStatus[index], sending: true, error: null };
      return newStatus;
    });
    
    try {
      // Wait for 1 second to simulate sending
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mark as sent using functional update for latest state
      setSendingStatus(prevStatus => {
        const newStatus = [...prevStatus];
        newStatus[index] = { ...newStatus[index], sending: false, sent: true };
        
        // Check if all emails are sent
        const allSent = newStatus.every(status => status.sent);
        if (allSent) {
          // Use setTimeout to avoid state update during render
          setTimeout(() => setAllSent(true), 0);
        }
        
        return newStatus;
      });
    } catch (err) {
      setSendingStatus(prevStatus => {
        const newStatus = [...prevStatus];
        newStatus[index] = { ...newStatus[index], sending: false, error: err.message };
        return newStatus;
      });
    }
  };

  // Send all emails
  const sendAllEmails = async () => {
    setSending(true);
    setError("");
    
    try {
      // Process each email one by one
      for (let i = 0; i < invoices.length; i++) {
        // Skip already sent emails (check current state)
        if (sendingStatus[i].sent) continue;
        
        // Mark current as sending (using functional update to ensure latest state)
        setSendingStatus(prevStatus => {
          const newStatus = [...prevStatus];
          newStatus[i] = { ...newStatus[i], sending: true, error: null };
          return newStatus;
        });
        
        // Wait for 1 second
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mark as sent (using functional update to ensure latest state)
        setSendingStatus(prevStatus => {
          const newStatus = [...prevStatus];
          newStatus[i] = { ...newStatus[i], sending: false, sent: true };
          
          // Check if all emails are sent now
          const allSent = newStatus.every(status => status.sent);
          if (allSent) {
            // Use setTimeout to avoid state update during render
            setTimeout(() => setAllSent(true), 0);
          }
          
          return newStatus;
        });
      }
    } catch (err) {
      setError("An error occurred while sending emails");
    } finally {
      setSending(false);
    }
  };
  
  // Go to home page - back to the initial page
  const goToHome = () => {
    // Reset all state and go back to initial page (step 0)
    onComplete && onComplete(true);
  };
  
  // Preview the email template with actual customer data
  const previewEmail = (invoice) => {
    const { emailSubject, emailBody, senderEmail } = emailTemplate;
    
    const renderedSubject = emailSubject
      .replace(/\{\{customer_name\}\}/g, invoice.customerName)
      .replace(/\{\{customer\}\}/g, invoice.customerName)
      .replace(/\{\{invoice_number\}\}/g, invoice.id)
      .replace(/\{\{total\}\}/g, invoice.amount)
      .replace(/\{\{company\}\}/g, "Company Name")
      .replace(/\{\{signature\}\}/g, "John Doe");
      
    const renderedBody = emailBody
      .replace(/\{\{customer_name\}\}/g, invoice.customerName)
      .replace(/\{\{customer\}\}/g, invoice.customerName)
      .replace(/\{\{invoice_number\}\}/g, invoice.id)
      .replace(/\{\{total\}\}/g, invoice.amount)
      .replace(/\{\{company\}\}/g, "Company Name")
      .replace(/\{\{signature\}\}/g, "John Doe");
      
    return { 
      subject: renderedSubject, 
      body: renderedBody,
      from: senderEmail
    };
  };

  return (
    <motion.div
      className="flex flex-col gap-8 w-full"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Send Invoices via Email
      </h2>
      
      <p className="text-gray-600 text-center max-w-3xl mx-auto">
        Review and send emails with the attached invoices to your customers.
      </p>

      {allSent && (
        <div className="bg-green-50 border border-green-200 rounded-md p-6 mb-6 text-center">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-green-100 text-green-600 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">All Emails Sent Successfully!</h3>
          <p className="text-gray-600 max-w-md mx-auto mb-4">
            All your invoices have been emailed to the respective customers. They should receive them shortly.
          </p>
        </div>
      )}

      {/* Only show the Send All button if not all emails are sent and we're not currently sending all */}
      {!allSent && !sending && (
        <div className="flex justify-center gap-4 mb-6">
          <button
            className="flex items-center gap-2 px-6 py-3 rounded-lg shadow-md bg-green-600 hover:bg-green-700 text-white"
            onClick={sendAllEmails}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
            </svg>
            Send All
          </button>
        </div>
      )}
      
      {/* Show sending indicator when in sending mode */}
      {sending && (
        <div className="flex justify-center items-center gap-2 text-blue-600 mb-6">
          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span>Sending emails...</span>
        </div>
      )}
      
      <div className="space-y-6">
        {sendingStatus.map((status, index) => {
          const invoice = invoices[index];
          const emailPreview = previewEmail(invoice);
          
          return (
            <div 
              key={invoice.id} 
              className={`border rounded-lg overflow-hidden shadow-sm ${
                status.sent ? 'bg-green-50 border-green-200' : 
                status.error ? 'bg-red-50 border-red-200' : 
                'bg-white'
              }`}
            >
              <div className="bg-gray-50 p-4 border-b flex justify-between items-center">
                <h3 className="font-medium text-gray-800">Email to: {invoice.customerName}</h3>
                <div className="flex items-center gap-2">
                  {status.sent ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Sent
                    </span>
                  ) : status.error ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                      Failed
                    </span>
                  ) : status.sending ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Sending...
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      Pending
                    </span>
                  )}
                  
                  {!status.sent && !status.sending && !sending && (
                    <button
                      onClick={() => sendSingleEmail(index)}
                      className="ml-2 inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
                    >
                      Send
                    </button>
                  )}
                </div>
              </div>
              
              <div className="p-4">
                <div className="bg-white p-3 rounded-lg border border-gray-200 mb-2">
                  <p className="text-xs text-gray-600">From: <span className="font-medium text-gray-800">{emailPreview.from}</span></p>
                </div>
                
                <div className="bg-white p-3 rounded-lg border border-gray-200 mb-4">
                  <p className="font-medium text-sm text-gray-800">Subject: {emailPreview.subject}</p>
                </div>
                
                <div className="bg-white p-3 rounded-lg border border-gray-200">
                  <p className="text-sm text-gray-600 whitespace-pre-wrap">{emailPreview.body}</p>
                </div>
                
                <div className="mt-3 flex items-center text-xs text-gray-500">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                  Invoice #{invoice.id}.pdf (attached)
                </div>
                
                {status.error && (
                  <div className="mt-3 text-sm text-red-600">
                    Error: {status.error}. <button className="underline" onClick={() => sendSingleEmail(index)}>Retry</button>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
      
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4 my-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}
    </motion.div>
  );
};

export default EmailSending; 