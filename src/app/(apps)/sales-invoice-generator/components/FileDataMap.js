import { motion } from "framer-motion";
import { useRef, useState } from "react";

const FileDataMap = ({ variants, onComplete }) => {
  const [file, setFile] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [columnData, setColumnData] = useState(null);
  const [mappings, setMappings] = useState({});
  const fileInputRef = useRef(null);

  // Predefined invoice field names
  const predefinedFields = [
    { id: "invoiceNumber", label: "Invoice Number" },
    { id: "date", label: "Date" },
    { id: "dueDate", label: "Due Date" },
    { id: "customerName", label: "Customer Name" },
    { id: "customerEmail", label: "Customer Email" },
    { id: "description", label: "Item Description" },
    { id: "quantity", label: "Quantity" },
    { id: "price", label: "Price" },
    { id: "subtotal", label: "Subtotal" },
    { id: "tax", label: "Tax" },
    { id: "total", label: "Total" }
  ];

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;
    
    // Check if file is Excel or CSV
    const validFileTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv'];
    if (!validFileTypes.includes(selectedFile.type)) {
      setError("Please upload an Excel or CSV file");
      return;
    }
    
    setFile(selectedFile);
    setError("");
  };

  // Process the file through a dummy API
  const processFile = async () => {
    if (!file) {
      setError("Please select a file first");
      return;
    }

    setLoading(true);
    
    try {
      // Simulate API call to process the file
      const response = await simulateFileProcessing(file);
      setColumnData(response);
      
      // Pre-select required fields with default values
      const initialMappings = {};
      predefinedFields.forEach(field => {
        initialMappings[field.id] = "";
      });
      
      // Try to intelligently match columns to fields
      if (response.columns) {
        response.columns.forEach(column => {
          const lowerColumn = column.toLowerCase();
          
          // Match invoice number
          if (lowerColumn.includes('invoice') || lowerColumn.includes('#')) {
            initialMappings.invoiceNumber = column;
          }
          // Match date
          else if (lowerColumn.includes('issue') || lowerColumn === 'date') {
            initialMappings.date = column;
          }
          // Match customer name
          else if (lowerColumn.includes('client') || lowerColumn.includes('customer')) {
            initialMappings.customerName = column;
          }
          // Match other fields
          else if (lowerColumn.includes('due')) {
            initialMappings.dueDate = column;
          }
          else if (lowerColumn.includes('email')) {
            initialMappings.customerEmail = column;
          }
          else if (lowerColumn.includes('product') || lowerColumn.includes('description')) {
            initialMappings.description = column;
          }
          else if (lowerColumn.includes('qty') || lowerColumn.includes('quantity')) {
            initialMappings.quantity = column;
          }
          else if (lowerColumn.includes('price') || lowerColumn.includes('rate')) {
            initialMappings.price = column;
          }
          else if (lowerColumn.includes('subtotal')) {
            initialMappings.subtotal = column;
          }
          else if (lowerColumn.includes('tax')) {
            initialMappings.tax = column;
          }
          else if (lowerColumn.includes('total')) {
            initialMappings.total = column;
          }
        });
      }
      
      setMappings(initialMappings);
      
    } catch (err) {
      setError("Failed to process the file. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Dummy API function to simulate file processing
  const simulateFileProcessing = (file) => {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate column data returned from API
        resolve({
          columns: [
            "Invoice #", 
            "Issue Date", 
            "Due Date", 
            "Client", 
            "Email", 
            "Product", 
            "Qty", 
            "Unit Price", 
            "Subtotal", 
            "Tax Rate", 
            "Total Amount"
          ],
          sampleData: [
            {
              "Invoice #": "INV-2023-001",
              "Issue Date": "2023-09-01",
              "Due Date": "2023-09-15",
              "Client": "Acme Corp",
              "Email": "<EMAIL>",
              "Product": "Website Design",
              "Qty": "1",
              "Unit Price": "1200",
              "Subtotal": "1200",
              "Tax Rate": "10%",
              "Total Amount": "1320"
            }
          ]
        });
      }, 1500);
    });
  };

  // Handle column mapping change
  const handleMappingChange = (fieldId, columnName) => {
    setMappings({
      ...mappings,
      [fieldId]: columnName
    });
  };

  // Handle completion of mapping
  const handleComplete = async () => {
    // Check if all required fields are mapped
    const requiredFields = ["invoiceNumber", "date", "customerName"];
    const missingRequiredFields = requiredFields.filter(field => !mappings[field]);
    
    if (missingRequiredFields.length > 0) {
      setError(`Please map the following required fields: ${missingRequiredFields.map(f => predefinedFields.find(pf => pf.id === f).label).join(", ")}`);
      return;
    }
    
    setLoading(true);
    try {
      // Call the dummy API to generate invoices
      const response = await generateInvoices(mappings);
      onComplete(response);
    } catch (err) {
      setError("Failed to generate invoices. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Dummy API function to generate invoices
  const generateInvoices = async (mappings) => {
    // Simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          invoices: [
            {
              id: "INV-2023-001",
              customerName: "Acme Corp",
              amount: "$1,320.00",
              date: "2023-09-01",
              downloadUrl: "/api/invoices/download/INV-2023-001",
              previewUrl: "https://placehold.co/600x800/e2e8f0/1e293b?text=Invoice+Preview+1"
            },
            {
              id: "INV-2023-002",
              customerName: "Globex Inc",
              amount: "$2,450.00",
              date: "2023-09-02",
              downloadUrl: "/api/invoices/download/INV-2023-002",
              previewUrl: "https://placehold.co/600x800/e2e8f0/1e293b?text=Invoice+Preview+2"
            }
          ],
          zipDownloadUrl: "/api/invoices/download/all",
        });
      }, 2000);
    });
  };

  // Render file upload section if no file is selected yet
  if (!columnData) {
    return (
      <motion.div
        className="flex flex-col items-center gap-6 w-full max-w-3xl mx-auto"
        variants={variants}
      >
        <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
          Upload Your Data File
        </h2>
        
        <p className="text-gray-600 text-center max-w-2xl">
          Upload an Excel or CSV file containing your invoice data to map columns to the template fields.
        </p>
        
        <div className="w-full max-w-2xl">
          <div 
            className="border-2 border-dashed border-gray-300 rounded-lg p-10 text-center cursor-pointer hover:border-blue-400 transition-colors"
            onClick={() => fileInputRef.current?.click()}
          >
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleFileChange}
              accept=".xlsx,.xls,.csv"
              className="hidden"
            />
            
            <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mx-auto text-gray-600 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            
            {file ? (
              <div>
                <p className="font-medium text-gray-800 text-xl mb-2">{file.name}</p>
                <p className="text-gray-600">{(file.size / 1024).toFixed(1)} KB</p>
              </div>
            ) : (
              <div>
                <p className="text-gray-700 text-xl font-medium mb-3">Click to select an Excel or CSV file</p>
                <p className="text-gray-600 text-md">or drag and drop here</p>
              </div>
            )}
          </div>
          
          {error && (
            <p className="text-red-600 text-sm mt-2">{error}</p>
          )}
          
          <button
            className={`mt-8 w-full py-4 rounded-lg font-medium text-lg ${file 
              ? 'bg-blue-600 hover:bg-blue-700 text-white' 
              : 'bg-gray-300 text-gray-600 cursor-not-allowed'}`}
            disabled={!file || loading}
            onClick={processFile}
          >
            {loading ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </div>
            ) : (
              "Process File"
            )}
          </button>
        </div>
      </motion.div>
    );
  }

  // Render column mapping interface if file has been processed
  return (
    <motion.div
      className="flex flex-col gap-6 w-full"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Map Your Data Columns
      </h2>
      
      <p className="text-gray-600 text-center max-w-3xl mx-auto">
        Select which columns from your file correspond to each invoice field.
      </p>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Required Fields Section */}
        <div className="space-y-5">
          <h3 className="text-lg font-medium text-gray-700">Required Fields</h3>
          
          <div className="border rounded-lg bg-white p-6 shadow-sm space-y-4">
            {predefinedFields
              .filter(field => ["invoiceNumber", "date", "customerName"].includes(field.id))
              .map(field => (
                <div key={field.id} className="flex flex-col space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {field.label} <span className="text-red-500">*</span>
                  </label>
                  <select
                    className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-800"
                    value={mappings[field.id] || ""}
                    onChange={(e) => handleMappingChange(field.id, e.target.value)}
                  >
                    <option value="" className="text-gray-800">Select column</option>
                    {columnData.columns.map((column, index) => (
                      <option key={index} value={column} className="text-gray-800">{column}</option>
                    ))}
                  </select>
                </div>
              ))
            }
          </div>
        </div>
        
        {/* Optional Fields Section */}
        <div className="lg:col-span-2 space-y-5">
          <h3 className="text-lg font-medium text-gray-700">Optional Fields</h3>
          
          <div className="border rounded-lg bg-white p-6 shadow-sm grid grid-cols-1 md:grid-cols-2 gap-4">
            {predefinedFields
              .filter(field => !["invoiceNumber", "date", "customerName"].includes(field.id))
              .map(field => (
                <div key={field.id} className="flex flex-col space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {field.label}
                  </label>
                  <select
                    className="border border-gray-300 rounded-md px-3 py-2 bg-white text-gray-800"
                    value={mappings[field.id] || ""}
                    onChange={(e) => handleMappingChange(field.id, e.target.value)}
                  >
                    <option value="" className="text-gray-800">Select column</option>
                    {columnData.columns.map((column, index) => (
                      <option key={index} value={column} className="text-gray-800">{column}</option>
                    ))}
                  </select>
                </div>
              ))
            }
          </div>
        </div>
      </div>
      
      {/* Sample Data Preview */}
      <div className="mt-6 border rounded-lg overflow-hidden shadow-sm">
        <div className="bg-gray-50 py-4 px-6 border-b">
          <h3 className="text-lg font-medium text-gray-700">Sample Data</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-100">
              <tr>
                {columnData.columns.map((column, index) => (
                  <th key={index} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {column}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {columnData.sampleData.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {columnData.columns.map((column, colIndex) => (
                    <td key={colIndex} className="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                      {row[column]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {error && (
        <p className="text-red-600 text-sm mt-2 text-center">{error}</p>
      )}
      
      <div className="flex justify-center mt-8">
        <button
          className={`py-4 px-10 rounded-lg font-medium text-lg ${
            loading ? 'bg-blue-500 text-white cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
          onClick={handleComplete}
          disabled={loading}
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Generating Invoices...
            </div>
          ) : (
            "Generate Invoices"
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default FileDataMap; 