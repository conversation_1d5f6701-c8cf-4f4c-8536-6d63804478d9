import { motion } from "framer-motion";
import Image from 'next/image';
import { useEffect, useRef, useState } from "react";

const TemplatePreview = ({ data, customLogo, onLogoChange, onProceed, variants }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const logoInputRef = useRef(null);
  const [editableFields, setEditableFields] = useState([]);
  const [editingField, setEditingField] = useState(null);
  
  // Extract all field names from the data
  const extractFieldNames = () => {
    if (!data?.fields) return [];
    
    let fieldNames = [];
    
    // Get basic field names
    Object.keys(data.fields).forEach(key => {
      if (key !== 'items') {
        fieldNames.push(key);
      }
    });
    
    // Add item fields with suffixes for multiple items
    if (data.fields.items && data.fields.items.length > 0) {
      const itemKeys = Object.keys(data.fields.items[0]);
      for (let i = 0; i < data.fields.items.length; i++) {
        itemKeys.forEach(key => {
          fieldNames.push(`item${i+1}_${key}`);
        });
      }
    }
    
    return fieldNames;
  };
  
  // Initialize editable fields when data changes
  useEffect(() => {
    if (data?.fields) {
      setEditableFields(extractFieldNames().map(name => ({
        id: name,
        name: formatFieldName(name),
        editable: true
      })));
    }
  }, [data, extractFieldNames]);
  
  // Handle logo file selection
  const handleLogoChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;
    
    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      setError("Please upload an image file for the logo");
      return;
    }
    
    setError("");
    setIsLoading(true);
    
    // Create a URL for the image preview
    const reader = new FileReader();
    reader.onload = (event) => {
      onLogoChange(event.target.result);
      setIsLoading(false);
    };
    reader.onerror = () => {
      setError("Failed to load the image");
      setIsLoading(false);
    };
    reader.readAsDataURL(file);
  };

  // Start editing a field
  const startEditing = (fieldId) => {
    setEditingField(fieldId);
  };

  // Save edited field name
  const saveFieldName = (fieldId, newName) => {
    setEditableFields(editableFields.map(field => 
      field.id === fieldId ? { ...field, name: newName } : field
    ));
    setEditingField(null);
  };

  // Handle field name change
  const handleFieldNameChange = (e, fieldId) => {
    if (e.key === 'Enter') {
      saveFieldName(fieldId, e.target.value);
    }
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingField(null);
  };

  return (
    <motion.div 
      className="flex flex-col gap-6 w-full"
      variants={variants}
    >
      <h2 className="text-xl font-medium text-gray-700 text-center mb-2">
        Review Your Template
      </h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Side - Invoice Preview */}
        <div className="flex flex-col gap-4">
          <h3 className="text-lg font-medium text-gray-700">Invoice Preview</h3>
          <div className="border border-gray-200 rounded-lg overflow-hidden bg-gray-50 shadow-sm">
            {data?.invoiceImage ? (
              <div className="relative w-full h-64 md:h-96">
                <Image 
                  src={data.invoiceImage} 
                  alt="Invoice Preview" 
                  fill
                  sizes="(max-width: 1024px) 100vw, 33vw"
                  style={{
                    objectFit: 'contain'
                  }}
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-64 bg-gray-100">
                <p className="text-gray-600">No preview available</p>
              </div>
            )}
          </div>
        </div>
        
        {/* Right Side - Logo and Fields */}
        <div className="lg:col-span-2 flex flex-col gap-6">
          {/* Logo Section */}
          <div className="flex flex-col gap-4">
            <h3 className="text-lg font-medium text-gray-700">Company Logo</h3>
            <div className="flex items-center gap-4">
              <div className="w-40 h-24 border border-gray-200 rounded-lg overflow-hidden flex items-center justify-center bg-white">
                {(customLogo || data?.logoImage) ? (
                  <div className="relative w-full h-full">
                    <Image 
                      src={customLogo || data?.logoImage} 
                      alt="Company Logo" 
                      fill
                      sizes="160px"
                      style={{
                        objectFit: 'contain'
                      }}
                    />
                  </div>
                ) : (
                  <p className="text-gray-600">No logo</p>
                )}
              </div>
              
              <div className="flex flex-col gap-2">
                <button
                  className="px-4 py-2 bg-blue-600 text-white font-medium rounded-lg text-sm hover:bg-blue-700 transition-colors"
                  onClick={() => logoInputRef.current?.click()}
                >
                  {data?.logoImage ? "Change Logo" : "Upload Logo"}
                </button>
                <input
                  type="file"
                  ref={logoInputRef}
                  onChange={handleLogoChange}
                  accept="image/*"
                  className="hidden"
                />
                {error && (
                  <p className="text-red-600 text-xs">{error}</p>
                )}
              </div>
            </div>
          </div>
          
          {/* Extracted Fields */}
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-700">Extracted Fields</h3>
              <p className="text-sm text-gray-600">Click to edit field names</p>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4 bg-white h-80 overflow-y-auto">
              {editableFields.length > 0 ? (
                <ul className="space-y-2">
                  {editableFields.map((field) => (
                    <li 
                      key={field.id}
                      className="relative group w-full"
                    >
                      {editingField === field.id ? (
                        <div className="flex">
                          <input
                            type="text"
                            defaultValue={field.name}
                            className="w-full p-2 text-sm border border-blue-500 rounded bg-white text-gray-800"
                            autoFocus
                            onBlur={(e) => saveFieldName(field.id, e.target.value)}
                            onKeyDown={(e) => handleFieldNameChange(e, field.id)}
                          />
                          <button 
                            className="absolute right-1 top-1/2 transform -translate-y-1/2 text-gray-600 hover:text-gray-800"
                            onClick={cancelEditing}
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      ) : (
                        <div 
                          className="bg-gray-50 p-3 rounded border border-gray-200 text-sm text-gray-800 cursor-pointer hover:bg-blue-50 hover:border-blue-300 flex justify-between items-center"
                          onClick={() => startEditing(field.id)}
                        >
                          <span>{field.name}</span>
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 text-gray-600 opacity-0 group-hover:opacity-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                          </svg>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-600 text-center py-4">No fields extracted</p>
              )}
            </div>
          </div>
          
          {/* Help text for navigation */}
          <div className="text-sm text-gray-600 flex justify-end mt-4">
            <button
              className="px-6 py-2 bg-blue-600 text-white font-medium rounded-lg text-sm hover:bg-blue-700 transition-colors"
              onClick={onProceed}
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Helper function to format field names for display
const formatFieldName = (name) => {
  return name
    .replace(/([A-Z])/g, ' $1') // Add space before capital letters
    .replace(/_/g, ' ') // Replace underscores with spaces
    .replace(/^\w/, c => c.toUpperCase()); // Capitalize first letter
};

export default TemplatePreview; 