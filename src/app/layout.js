import "./globals.css";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/context/AuthContext";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import { CreditProvider } from "@/context/CreditContext";
import GoogleOAuthProvider from "@/components/auth/GoogleOAuthProvider";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "MizuFlow",
  description: "MizuFlow financial automation services platform",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gray-100`}
      >
        <GoogleOAuthProvider>
          <AuthProvider>
            <CreditProvider>
              {children}
              <Toaster position="top-right" />
            </CreditProvider>
          </AuthProvider>
        </GoogleOAuthProvider>
      </body>
    </html>
  );
}
